<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiToolNotFoundException;
use App\V2\Domain\LTI\LtiTool;
use App\V2\Domain\LTI\LtiToolCollection;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Url\InvalidUrlException;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBAL\CommonCriteriaBuilder;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;

class DBALLtiToolRepository implements LtiToolRepository
{
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $em,
        private readonly string $ltiToolTableName,
    ) {
        $this->connection = $em->getConnection();
    }

    #[\Override]
    public function put(LtiTool $ltiTool): void
    {
        try {
            $this->findOneBy(
                LtiToolCriteria::createById($ltiTool->getId())
            );

            $this->update($ltiTool);
        } catch (LtiToolNotFoundException) {
            $this->insert($ltiTool);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function insert(LtiTool $tool): void
    {
        try {
            $this->connection->insert(
                table: $this->ltiToolTableName,
                data: $this->fromToolToArray($tool),
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function update(LtiTool $tool): void
    {
        try {
            $data = $this->fromToolToArray($tool);
            unset($data['id']);

            $this->connection->update(
                table: $this->ltiToolTableName,
                data: $data,
                criteria: ['id' => $tool->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(LtiToolCriteria $criteria): LtiTool
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new LtiToolNotFoundException();
            }

            return $this->fromArrayToTool($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(LtiToolCriteria $criteria): LtiToolCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new LtiToolCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToTool($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(LtiTool $ltiTool): void
    {
        try {
            $this->connection->delete(
                table: $this->ltiToolTableName,
                criteria: ['id' => $ltiTool->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    private function fromToolToArray(LtiTool $tool): array
    {
        return [
            'id' => $tool->getId()->value(),
            'registration_id' => $tool->getRegistrationId()->value(),
            'name' => $tool->getName(),
            'audience' => $tool->getAudience(),
            'oidc_initiation_url' => $tool->getOidcInitiationUrl()->value(),
            'launch_url' => $tool->getLaunchUrl()->value(),
            'deep_linking_url' => $tool->getDeepLinkingUrl()->value(),
            'jwks_url' => $tool->getJwksUrl()->value(),
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws InvalidUrlException
     */
    private function fromArrayToTool(array $values): LtiTool
    {
        return new LtiTool(
            id: new Uuid($values['id']),
            registrationId: new Uuid($values['registration_id']),
            name: $values['name'],
            audience: $values['audience'],
            oidcInitiationUrl: new Url($values['oidc_initiation_url']),
            launchUrl: new Url($values['launch_url']),
            deepLinkingUrl: new Url($values['deep_linking_url']),
            jwksUrl: new Url($values['jwks_url']),
        );
    }

    private function getQueryBuilderByCriteria(LtiToolCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->ltiToolTableName, 't');

        if (null !== $criteria->getRegistrationId()) {
            $qb->andWhere('t.registration_id = :registration_id')
                ->setParameter('registration_id', $criteria->getRegistrationId()->value());
        }

        if (null !== $criteria->getRegistrationIds() && !$criteria->getRegistrationIds()->isEmpty()) {
            $qb->andWhere('t.registration_id IN (:registration_ids)')
                ->setParameter('registration_ids', $criteria->getRegistrationIds()->all(), ArrayParameterType::STRING);
        }

        if (null !== $criteria->getAudience()) {
            $qb->andWhere('t.audience = :audience')
                ->setParameter('audience', $criteria->getAudience());
        }

        CommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb);

        return $qb;
    }
}
