<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiPlatformNotFoundException;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\LTI\LtiPlatformCollection;
use App\V2\Domain\LTI\LtiPlatformCriteria;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Url\InvalidUrlException;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBAL\CommonCriteriaBuilder;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;

class DBALLtiPlatformRepository implements LtiPlatformRepository
{
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $em,
        private readonly string $ltiPlatformTableName,
    ) {
        $this->connection = $em->getConnection();
    }

    #[\Override]
    public function put(LtiPlatform $platform): void
    {
        try {
            $this->findOneBy(
                LtiPlatformCriteria::createById($platform->getId())
            );

            $this->update($platform);
        } catch (LtiPlatformNotFoundException) {
            $this->insert($platform);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function insert(LtiPlatform $platform): void
    {
        try {
            $this->connection->insert(
                table: $this->ltiPlatformTableName,
                data: $this->fromPlatformToArray($platform),
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function update(LtiPlatform $platform): void
    {
        try {
            $data = $this->fromPlatformToArray($platform);
            unset($data['id']);

            $this->connection->update(
                table: $this->ltiPlatformTableName,
                data: $data,
                criteria: ['id' => $platform->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(LtiPlatformCriteria $criteria): LtiPlatform
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new LtiPlatformNotFoundException();
            }

            return $this->fromArrayToPlatform($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(LtiPlatformCriteria $criteria): LtiPlatformCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new LtiPlatformCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToPlatform($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(LtiPlatform $platform): void
    {
        try {
            $this->connection->delete(
                table: $this->ltiPlatformTableName,
                criteria: ['id' => $platform->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InvalidUrlException
     * @throws InvalidUuidException
     */
    private function fromArrayToPlatform(array $values): LtiPlatform
    {
        return new LtiPlatform(
            id: new Uuid($values['id']),
            registrationId: new Uuid($values['registration_id']),
            name: $values['name'],
            audience: $values['audience'],
            oidcAuthenticationUrl: new Url($values['oidc_authentication_url']),
            oauth2AccessTokenUrl: new Url($values['oauth2_access_token_url']),
            jwksUrl: new Url($values['jwks_url']),
        );
    }

    private function fromPlatformToArray(LtiPlatform $platform): array
    {
        return [
            'id' => $platform->getId()->value(),
            'registration_id' => $platform->getRegistrationId()->value(),
            'name' => $platform->getName(),
            'audience' => $platform->getAudience(),
            'oidc_authentication_url' => $platform->getOidcAuthenticationUrl()->value(),
            'oauth2_access_token_url' => $platform->getOauth2AccessTokenUrl()->value(),
            'jwks_url' => $platform->getJwksUrl()->value(),
        ];
    }

    private function getQueryBuilderByCriteria(LtiPlatformCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->ltiPlatformTableName, 't');

        if (null !== $criteria->getRegistrationId()) {
            $qb->andWhere('t.registration_id = :registration_id')
                ->setParameter('registration_id', $criteria->getRegistrationId()->value());
        }

        if (null !== $criteria->getRegistrationIds() && !$criteria->getRegistrationIds()->isEmpty()) {
            $qb->andWhere('t.registration_id IN (:registration_ids)')
                ->setParameter('registration_ids', $criteria->getRegistrationIds()->all(), ArrayParameterType::STRING);
        }

        if (null !== $criteria->getAudience()) {
            $qb->andWhere('t.audience = :audience')
                ->setParameter('audience', $criteria->getAudience());
        }

        CommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb);

        return $qb;
    }
}
