<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBAL\CommonCriteriaBuilder;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;

readonly class DBALLtiRegistrationRepository implements LtiRegistrationRepository
{
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $em,
        private string $ltiRegistrationTableName,
    ) {
        $this->connection = $em->getConnection();
    }

    /**
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function put(LtiRegistration $registration): void
    {
        try {
            $result = $this->findOneBy(
                LtiRegistrationCriteria::createEmpty()
                    ->filterByClientId($registration->getClientId())
            );

            if (!$result->getId()->equals($registration->getId())) {
                throw LtiException::clientIdMustBeUnique();
            }
        } catch (LtiRegistrationNotFoundException) {
        }

        try {
            $this->findOneBy(
                LtiRegistrationCriteria::createById($registration->getId())
            );

            $this->update($registration);
        } catch (LtiRegistrationNotFoundException) {
            $this->insert($registration);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function insert(LtiRegistration $registration): void
    {
        try {
            $this->connection->insert(
                table: $this->ltiRegistrationTableName,
                data: $this->fromRegistrationToArray($registration),
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function update(LtiRegistration $registration): void
    {
        try {
            $values = $this->fromRegistrationToArray($registration);
            unset($values['id']);

            $this->connection->update(
                table: $this->ltiRegistrationTableName,
                data: $values,
                criteria: ['id' => $registration->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    public function findOneBy(LtiRegistrationCriteria $criteria): LtiRegistration
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new LtiRegistrationNotFoundException();
            }

            return $this->fromArrayToRegistration($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    public function findBy(LtiRegistrationCriteria $criteria): LtiRegistrationCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new LtiRegistrationCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToRegistration($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    public function delete(LtiRegistration $ltiRegistration): void
    {
        try {
            $this->connection->delete(
                table: $this->ltiRegistrationTableName,
                criteria: ['id' => $ltiRegistration->getId()->value()],
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InvalidUuidException
     */
    private function fromArrayToRegistration(array $values): LtiRegistration
    {
        return new LtiRegistration(
            id: new Uuid($values['id']),
            name: $values['name'],
            clientId: $values['client_id'],
        );
    }

    private function fromRegistrationToArray(LtiRegistration $registration): array
    {
        return [
            'id' => $registration->getId()->value(),
            'name' => $registration->getName(),
            'client_id' => $registration->getClientId(),
        ];
    }

    private function getQueryBuilderByCriteria(LtiRegistrationCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->ltiRegistrationTableName, 't');

        if (null !== $criteria->getClientId()) {
            $qb->andWhere('t.client_id = :clientId')
                ->setParameter('clientId', $criteria->getClientId());
        }

        if (null !== $criteria->getSearchString()) {
            $qb->andWhere('t.name LIKE :searchString')
                ->setParameter('searchString', '%' . $criteria->getSearchString() . '%');
        }

        CommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb);

        return $qb;
    }
}
